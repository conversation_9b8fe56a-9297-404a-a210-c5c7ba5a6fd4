/**
 * @fileoverview
 * Centralized color constants for Party Reservation application
 * Following industry best practices for maintainable color management
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-28
 */

/**
 * Color palette interface for type safety
 */
export interface ColorPalette {
  primary: {
    green: string;
    blue: string;
    black: string;
  };
  feedback: {
    success: string;
    error: string;
  };
  neutral: {
    dark: string;
    medium: string;
  };
  surface: {
    border: string;
    background: string;
    lighter: string;
  };
}

/**
 * Centralized color constants
 * All hex colors used across party-reservation components
 */
export const PARTY_RESERVATION_COLORS: ColorPalette = {
  primary: {
    green: '#6AC79D',    // Used for pricing, success states, applied coupons
    blue: '#5171c7',     // Used for links, focus states
    black: '#171717',    // Used for text, buttons
  },
  feedback: {
    success: '#0BAB68',  // Used for success messages, applied coupon background
    error: '#c01a3a',    // Used for error states (can be extended)
  },
  neutral: {
    dark: '#747474',     // Used for secondary text, placeholders
    medium: '#a2a2a2',   // Used for medium contrast text
  },
  surface: {
    border: '#dce3f4',   // Used for input borders, dividers
    background: '#F5FBFF', // Used for card backgrounds
    lighter: '#f1f4fe',  // Used for quantity info backgrounds
  },
};

/**
 * Tailwind CSS class mappings for colors
 * Maps semantic color names to Tailwind classes
 */
export const TAILWIND_COLOR_CLASSES = {
  // Primary colors
  'text-primary-green': `text-[${PARTY_RESERVATION_COLORS.primary.green}]`,
  'bg-primary-green': `bg-[${PARTY_RESERVATION_COLORS.primary.green}]`,
  'text-primary-blue': `text-[${PARTY_RESERVATION_COLORS.primary.blue}]`,
  'ring-primary-blue': `ring-[${PARTY_RESERVATION_COLORS.primary.blue}]`,
  'text-primary-black': `text-[${PARTY_RESERVATION_COLORS.primary.black}]`,
  
  // Feedback colors
  'text-success': `text-[${PARTY_RESERVATION_COLORS.feedback.success}]`,
  'bg-success': `bg-[${PARTY_RESERVATION_COLORS.feedback.success}]`,
  
  // Neutral colors
  'text-neutral-dark': `text-[${PARTY_RESERVATION_COLORS.neutral.dark}]`,
  'text-neutral-medium': `text-[${PARTY_RESERVATION_COLORS.neutral.medium}]`,
  
  // Surface colors
  'border-surface': `border-[${PARTY_RESERVATION_COLORS.surface.border}]`,
  'bg-surface': `bg-[${PARTY_RESERVATION_COLORS.surface.background}]`,
  'bg-surface-lighter': `bg-[${PARTY_RESERVATION_COLORS.surface.lighter}]`,
} as const;

/**
 * CSS custom properties for use in component styles
 * Can be used in CSS files or style attributes
 */
export const CSS_COLOR_VARIABLES = {
  '--color-primary-green': PARTY_RESERVATION_COLORS.primary.green,
  '--color-primary-blue': PARTY_RESERVATION_COLORS.primary.blue,
  '--color-primary-black': PARTY_RESERVATION_COLORS.primary.black,
  '--color-success': PARTY_RESERVATION_COLORS.feedback.success,
  '--color-neutral-dark': PARTY_RESERVATION_COLORS.neutral.dark,
  '--color-neutral-medium': PARTY_RESERVATION_COLORS.neutral.medium,
  '--color-surface-border': PARTY_RESERVATION_COLORS.surface.border,
  '--color-surface-background': PARTY_RESERVATION_COLORS.surface.background,
  '--color-surface-lighter': PARTY_RESERVATION_COLORS.surface.lighter,
} as const;

/**
 * Utility function to get color value by semantic name
 * @param colorPath - Dot notation path to color (e.g., 'primary.green')
 * @returns Hex color value
 */
export function getColor(colorPath: string): string {
  const keys = colorPath.split('.');
  let value: any = PARTY_RESERVATION_COLORS;
  
  for (const key of keys) {
    value = value[key];
    if (value === undefined) {
      console.warn(`Color path '${colorPath}' not found`);
      return '#000000'; // fallback color
    }
  }
  
  return value;
}

/**
 * Utility function to get Tailwind class by semantic name
 * @param className - Semantic class name (e.g., 'text-primary-green')
 * @returns Tailwind CSS class string
 */
export function getTailwindClass(className: keyof typeof TAILWIND_COLOR_CLASSES): string {
  return TAILWIND_COLOR_CLASSES[className];
}
