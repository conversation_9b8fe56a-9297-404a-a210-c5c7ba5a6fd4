import { Component } from '@angular/core';
import { ReservationCardComponent } from '../../components/reservation-card/reservation-card';
import { SummaryCard } from '../../components/summary-card/summary-card';
import { GuestForm } from '../../components/guest-form/guest-form';
import { PriceDetails } from '../../components/price-details/price-details';
import { RouterOutlet } from '@angular/router';
import { DiscountCard } from '../../components/discount-card/discount-card';
import { PaymentOptionsComponent } from '../../components/payment-options/payment-options.component';
import { FooterComponent } from '../../components/footer/footer';
import { ColorService } from '../../services/color.service';
import { TAILWIND_COLOR_CLASSES } from '../../constants/colors.constant';


@Component({
  selector: 'app-billing-page',
  imports: [ 
    RouterOutlet,
    ReservationCardComponent,
    DiscountCard,
    PaymentOptionsComponent,
    PriceDetails,
    GuestForm,
    SummaryCard,
    FooterComponent
],
  templateUrl: './billing-page.html',
  styleUrl: './billing-page.css'
})
export class BillingPage {

  constructor(private colorService: ColorService) {}

  // Color getters for template use
  get colors() {
    return {
      neutralDark: TAILWIND_COLOR_CLASSES['text-neutral-dark'],
      primaryBlue: TAILWIND_COLOR_CLASSES['text-primary-blue'],
      accentPrimaryBlue: TAILWIND_COLOR_CLASSES['accent-primary-blue']
    };
  }
}
