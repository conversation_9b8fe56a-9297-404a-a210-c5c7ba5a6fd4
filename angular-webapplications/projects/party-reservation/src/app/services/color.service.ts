/**
 * @fileoverview
 * Color service for Party Reservation application
 * Provides centralized color management with theme support
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-28
 */

import { Injectable } from '@angular/core';
import { 
  PARTY_RESERVATION_COLORS, 
  TAILWIND_COLOR_CLASSES, 
  CSS_COLOR_VARIABLES,
  ColorPalette,
  getColor,
  getTailwindClass
} from '../constants/colors.constant';

@Injectable({
  providedIn: 'root'
})
export class ColorService {
  
  /**
   * Get the complete color palette
   */
  getColorPalette(): ColorPalette {
    return PARTY_RESERVATION_COLORS;
  }

  /**
   * Get a specific color by semantic path
   * @param colorPath - Dot notation path (e.g., 'primary.green')
   */
  getColor(colorPath: string): string {
    return getColor(colorPath);
  }

  /**
   * Get Tailwind CSS class for a semantic color name
   * @param className - Semantic class name
   */
  getTailwindClass(className: keyof typeof TAILWIND_COLOR_CLASSES): string {
    return getTailwindClass(className);
  }

  /**
   * Get all CSS custom properties for injection into components
   */
  getCSSVariables(): Record<string, string> {
    return CSS_COLOR_VARIABLES;
  }

  /**
   * Apply CSS custom properties to a component's host element
   * @param element - HTML element to apply variables to
   */
  applyCSSVariables(element: HTMLElement): void {
    Object.entries(CSS_COLOR_VARIABLES).forEach(([property, value]) => {
      element.style.setProperty(property, value);
    });
  }

  /**
   * Get commonly used color combinations for specific UI elements
   */
  getUIColorSchemes() {
    return {
      // For pricing displays
      pricing: {
        text: this.getColor('primary.green'),
        background: 'transparent'
      },
      
      // For success states (applied coupons, confirmations)
      success: {
        text: this.getColor('feedback.success'),
        background: this.getColor('feedback.success')
      },
      
      // For form inputs
      input: {
        text: this.getColor('neutral.dark'),
        border: this.getColor('surface.border'),
        focus: this.getColor('primary.blue')
      },
      
      // For card backgrounds
      card: {
        background: this.getColor('surface.background'),
        border: this.getColor('surface.border')
      },
      
      // For buttons
      primaryButton: {
        background: this.getColor('primary.black'),
        text: '#ffffff'
      },
      
      // For links
      link: {
        text: this.getColor('primary.blue'),
        hover: this.getColor('primary.blue')
      }
    };
  }
}
