import { Component } from '@angular/core';
import { ColorService } from '../../services/color.service';
import { TAILWIND_COLOR_CLASSES } from '../../constants/colors.constant';

@Component({
  selector: 'app-reservation-card',
  standalone: true,
  templateUrl: './reservation-card.html',
  styleUrls: ['./reservation-card.css']
})
export class ReservationCardComponent {

  constructor(private colorService: ColorService) {}

  // Color getters for template use
  get colors() {
    return {
      primaryGreen: TAILWIND_COLOR_CLASSES['text-primary-green'],
      primaryBlack: TAILWIND_COLOR_CLASSES['text-primary-black']
    };
  }
}