<div class="flex flex-col gap-5 md:w-[28.875rem] h-[18.6875rem] p-[1.5rem] rounded-[1.875rem] bg-white">
  <div class="flex flex-row items-center gap-2">
    <span class="text-[0.875rem] leading-[1.125rem] font-semibold text-neutral-900">{{ priceDetailsData.title }}</span>
  </div>
  <div class="flex flex-col gap-2 w-full">
    <!-- Dynamic Price Items -->
    @for (item of priceDetailsData.items; track item.label) {
      <div class="flex flex-row items-center w-full">
        <div class="flex-1 text-[0.875rem] leading-5 font-normal text-neutral-900">{{ item.label }}</div>
        <div class="text-[0.875rem] leading-5 font-normal text-neutral-900">{{ item.amount }}</div>
      </div>
    }
  </div>
  <div [class]="'my-2 border-t ' + colors.borderSurface"></div>
  <div class="flex flex-row items-baseline w-full">
    <div class="flex-1 text-[0.875rem] leading-5 font-normal text-neutral-900">{{ priceDetailsData.amountDue.label }}</div>
    <div class="text-[0.875rem] leading-5 font-bold text-neutral-900">{{ priceDetailsData.amountDue.amount }}</div>
  </div>
</div>

