import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ColorService } from '../../services/color.service';
import { TAILWIND_COLOR_CLASSES } from '../../constants/colors.constant';

interface PriceItem {
  label: string;
  amount: string;
  isBold?: boolean;
}

interface PriceDetailsData {
  title: string;
  items: PriceItem[];
  amountDue: {
    label: string;
    amount: string;
  };
}

@Component({
  selector: 'app-price-details',
  imports: [CommonModule],
  templateUrl: './price-details.html',
  styleUrl: './price-details.css'
})
export class PriceDetails {

  constructor(private colorService: ColorService) {}

  // Color getters for template use
  get colors() {
    return {
      borderSurface: TAILWIND_COLOR_CLASSES['border-surface']
    };
  }

  // Simulated API response data
  priceDetailsData: PriceDetailsData = {
    title: 'Price Details',
    items: [
      {
        label: 'Subtotal',
        amount: '$499.50'
      },
      {
        label: 'Tax',
        amount: '$40.00'
      },
      {
        label: 'Total',
        amount: '$539.00'
      },
      {
        label: 'Payment Done',
        amount: '$100.00'
      }
    ],
    amountDue: {
      label: 'Amount Due',
      amount: '$439.00'
    }
  };
}
