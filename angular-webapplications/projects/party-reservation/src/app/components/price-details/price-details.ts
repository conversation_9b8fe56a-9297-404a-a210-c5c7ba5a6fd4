import { Component } from '@angular/core';
import { ColorService } from '../../services/color.service';
import { TAILWIND_COLOR_CLASSES } from '../../constants/colors.constant';

@Component({
  selector: 'app-price-details',
  imports: [],
  templateUrl: './price-details.html',
  styleUrl: './price-details.css'
})
export class PriceDetails {

  constructor(private colorService: ColorService) {}

  // Color getters for template use
  get colors() {
    return {
      borderSurface: TAILWIND_COLOR_CLASSES['border-surface']
    };
  }
}
