import { Component } from '@angular/core';
import { ColorService } from '../../services/color.service';
import { TAILWIND_COLOR_CLASSES } from '../../constants/colors.constant';

@Component({
  selector: 'app-guest-form',
  imports: [],
  templateUrl: './guest-form.html',
  styleUrl: './guest-form.css'
})
export class GuestForm {

  constructor(private colorService: ColorService) {}

  // Color getters for template use
  get colors() {
    return {
      borderSurface: TAILWIND_COLOR_CLASSES['border-surface'],
      neutralDark: TAILWIND_COLOR_CLASSES['text-neutral-dark'],
      primaryBlue: TAILWIND_COLOR_CLASSES['text-primary-blue'],
      ringPrimaryBlue: TAILWIND_COLOR_CLASSES['ring-primary-blue']
    };
  }
}
