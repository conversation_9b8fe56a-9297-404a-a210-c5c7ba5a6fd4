<div
  class="flex flex-col w-full max-w-[21.5rem] md:max-w-[21.5rem] p-6 rounded-[1.875rem] bg-white"
>
  <div class="flex flex-row items-center">
    <span
      class="text-sm leading-[1.125rem] font-semibold text-neutral-900"
    >
      Discounts
    </span>
  </div>

  <div class="flex flex-col w-full mt-4 md:mt-5">
    <label
      class="text-xs leading-[1.125rem] font-normal text-neutral-900"
    >
      Coupons
    </label>

    <div [class]="'relative w-full mt-2 border rounded-xl overflow-hidden ' + colors.borderSurface">
      <input
        [(ngModel)]="couponCode"
        type="text"
        placeholder="Enter coupon code"
        [class]="'w-full h-12 pl-4 pr-20 py-[0.9375rem] rounded-xl text-sm focus:outline-none ' + colors.neutralDark"
        [readonly]="isApplied"
        [ngClass]="isApplied ? colors.primaryGreen : ''"
      />
      <button
        (click)="applyCoupon()"
        [disabled]="isButtonDisabled && !isApplied"
        class="absolute top-0 right-0 w-[5.6rem] h-full text-base font-medium bg-black-900 text-white transition-colors focus:outline-none z-10"
        [class.bg-gray-400]="isButtonDisabled && !isApplied"
        [class.cursor-not-allowed]="isButtonDisabled && !isApplied"
      >
        {{ isApplied ? 'Remove' : 'Apply' }}
      </button>
    </div>
    

    @if(isApplied) {
      <p [class]="'w-full mt-2 text-sm leading-[1.125rem] ' + colors.success">
        {{ couponCode }} applied. You saved $15
      </p>
    } @else if(isError) {
      <p class="w-full text-sm leading-[1.125rem] text-red-600">
        Invalid coupon code. Please try again.
      </p>
    }
  </div>
</div>

<!-- Modal Overlay -->
@if(showModal) {
  <div class="fixed inset-0 flex items-center justify-center bg-opacity-75 z-50 bg-black-900">
    <app-dicount-applied-modal
      [code]="couponCode"
      [discount]="15"
      (closeModal)="closeModal()">
    </app-dicount-applied-modal>
  </div>
}
