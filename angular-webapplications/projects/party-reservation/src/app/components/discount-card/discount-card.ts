import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NgClass } from '@angular/common';
import { DicountAppliedModal } from '../dicount-applied-modal/dicount-applied-modal';

@Component({
  selector: 'app-discount-card',
  standalone: true,
  imports: [FormsModule, NgClass, DicountAppliedModal],
  templateUrl: './discount-card.html',
  styleUrl: './discount-card.css'
})
export class DiscountCard {
  couponCode = '';
  isApplied = false;
  isError = false;
  showModal = false;

  applyCoupon() {
    if (!this.isApplied) {
      if (this.couponCode.trim().toUpperCase() === 'SUMMERDEAL15') {
        this.isApplied = true;
        this.isError = false;
        this.showModal = true; 
      } else {
        this.isApplied = false;
        this.isError = true;
      }
    } else {
      // Remove coupon
      this.isApplied = false;
      this.isError = false;
      this.couponCode = '';
    }
  }

  closeModal() {
    this.showModal = false;
  }

  get isButtonDisabled() {
    return !this.couponCode.trim();
  }
}
