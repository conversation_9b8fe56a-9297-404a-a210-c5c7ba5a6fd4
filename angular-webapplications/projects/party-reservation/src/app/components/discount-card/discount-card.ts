import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NgClass } from '@angular/common';
import { DicountAppliedModal } from '../dicount-applied-modal/dicount-applied-modal';
import { ColorService } from '../../services/color.service';
import { TAILWIND_COLOR_CLASSES } from '../../constants/colors.constant';

@Component({
  selector: 'app-discount-card',
  standalone: true,
  imports: [FormsModule, NgClass, DicountAppliedModal],
  templateUrl: './discount-card.html',
  styleUrl: './discount-card.css'
})
export class DiscountCard {
  couponCode = '';
  isApplied = false;
  isError = false;
  showModal = false;

  constructor(private colorService: ColorService) {}

  // Color getters for template use
  get colors() {
    return {
      borderSurface: TAILWIND_COLOR_CLASSES['border-surface'],
      neutralDark: TAILWIND_COLOR_CLASSES['text-neutral-dark'],
      primaryGreen: TAILWIND_COLOR_CLASSES['bg-primary-green'],
      success: TAILWIND_COLOR_CLASSES['text-success']
    };
  }

  applyCoupon() {
    if (!this.isApplied) {
      if (this.couponCode.trim().toUpperCase() === 'SUMMERDEAL15') {
        this.isApplied = true;
        this.isError = false;
        this.showModal = true; 
      } else {
        this.isApplied = false;
        this.isError = true;
      }
    } else {
      // Remove coupon
      this.isApplied = false;
      this.isError = false;
      this.couponCode = '';
    }
  }

  closeModal() {
    this.showModal = false;
  }

  get isButtonDisabled() {
    return !this.couponCode || this.couponCode.trim().length === 0;
  }
}
