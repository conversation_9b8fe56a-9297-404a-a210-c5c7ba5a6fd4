import { Component } from '@angular/core';
import { ReservationCardComponent } from '../reservation-card/reservation-card';
import { SummaryCard } from '../summary-card/summary-card';
import { GuestForm } from '../guest-form/guest-form';
import { PriceDetails } from '../price-details/price-details';
import { RouterOutlet } from '@angular/router';
import { DiscountCard } from '../discount-card/discount-card';
import { PaymentOptionsComponent } from '../payment-options/payment-options.component';


@Component({
  selector: 'app-billing-page',
  imports: [ 
    RouterOutlet,
    ReservationCardComponent,
    DiscountCard,
    PaymentOptionsComponent,
    PriceDetails,
    GuestForm,
    SummaryCard,
],
  templateUrl: './billing-page.html',
  styleUrl: './billing-page.css'
})
export class BillingPage {

}
