/**
 * @fileoverview Main Party Reservation Component
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2025-01-28
 */

import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

@Component({
    selector: 'app-party-reservation-main',
    standalone: true,
    imports: [CommonModule],
    templateUrl: './party-reservation-main.component.html',
    //styleUrl: './party-reservation-main.component.css'
})
export class PartyReservationMainComponent {
    title = 'Party Reservations';

    constructor(private router: Router) {}
    
    // Sample data for testing
    reservationTypes = [
        {
            id: 1,
            name: 'Birthday Party',
            description: 'Perfect for celebrating birthdays with friends and family',
            price: 299,
            duration: '3 hours',
            maxGuests: 20
        },
        {
            id: 2,
            name: 'Corporate Event',
            description: 'Professional space for team building and corporate gatherings',
            price: 599,
            duration: '4 hours',
            maxGuests: 50
        },
        {
            id: 3,
            name: 'Private Party',
            description: 'Exclusive venue for your special celebration',
            price: 899,
            duration: '6 hours',
            maxGuests: 100
        }
    ];

    onSelectReservationType(type: any) {
        console.log('Selected reservation type:', type);
        // TODO: Navigate to booking form or next step
    }
}
