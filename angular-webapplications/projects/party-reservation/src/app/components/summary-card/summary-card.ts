import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

interface PackageItem {
  name: string;
  priceDetails: string;
  totalPrice: string;
  hasQuantityInfo?: boolean;
  quantityInfo?: string;
}

interface PartyReservationData {
  title: string;
  basePrice: string;
  location: string;
  date: string;
  packageItems: PackageItem[];
}

@Component({
  selector: 'app-summary-card',
  imports: [CommonModule],
  templateUrl: './summary-card.html',
  styleUrl: './summary-card.css'
})
export class SummaryCard {
  // Simulated API response data
  reservationData: PartyReservationData = {
    title: 'Ultimate Adventure Party',
    basePrice: '$429.90',
    location: 'Semnox Luna',
    date: '17-07-2024',
    packageItems: [
      {
        name: 'Ultimate Adventure',
        priceDetails: '($42.99 X 10)',
        totalPrice: '$0.00',
        hasQuantityInfo: true,
        quantityInfo: 'Price Incl. Qty : 1'
      },
      {
        name: '$20 game credits per guest',
        priceDetails: '($42.99 X 10)',
        totalPrice: '$0.00'
      },
      {
        name: '$20 game credits per guest',
        priceDetails: '($42.99 X 10)',
        totalPrice: '$0.00'
      },
      {
        name: 'Ultimate Adventure',
        priceDetails: '($42.99 X 10)',
        totalPrice: '$0.00',
        hasQuantityInfo: true,
        quantityInfo: 'Price Incl. Qty : 1'
      },
      {
        name: '$20 game credits per guest',
        priceDetails: '($42.99 X 10)',
        totalPrice: '$0.00'
      },
      {
        name: '$20 game credits per guest',
        priceDetails: '($42.99 X 10)',
        totalPrice: '$0.00'
      },
      {
        name: '$20 game credits per guest',
        priceDetails: '($42.99 X 10)',
        totalPrice: '$0.00'
      },
      {
        name: '$20 game credits per guest',
        priceDetails: '($42.99 X 10)',
        totalPrice: '$0.00'
      }
    ]
  };
}
