<div
  class="flex flex-col gap-6 w-[21.5625rem] p-6 h-[43rem] rounded-[1.875rem] bg-white shadow-[0_6px_16px_0_rgba(137,176,189,0.18)]"
>
  <!-- Title and Base Price -->
  <div class="flex flex-col gap-2">
    <span
      class="text-[1.125rem] leading-[1.5rem] font-semibold text-neutral-900"
      >{{ reservationData.title }}</span
    >
    <span
      class="text-[0.875rem] leading-[1.125rem] font-semibold text-[#6ac79d]"
      >Base Price: {{ reservationData.basePrice }}</span
    >
  </div>
  <div class="flex flex-col gap-[0.75rem] p-[1.25rem] rounded-[1.25rem] bg-[#F5FBFF]">
    <div class="flex items-center gap-2">
      <img src="Location.svg" alt="Location" class="w-5 h-5" />
      <span class="text-[0.875rem] leading-5 font-normal text-neutral-900"
        >{{ reservationData.location }}</span
      >
    </div>
    <div class="flex items-center gap-2">
      <img src="Calendar.svg" alt="Calendar" class="w-5 h-5" />
      <span class="text-[0.875rem] leading-5 font-normal text-neutral-900"
        >{{ reservationData.date }}</span
      >
    </div>
  </div>
  <h2 class="text-[0.875rem] leading-[1.125rem] font-semibold">Package Info</h2>

    <!-- Dynamic Package Items -->
    <div *ngFor="let item of reservationData.packageItems" class="flex flex-row items-start justify-between w-full">
      <div class="flex flex-col">
        <span class="text-[0.875rem] leading-5 font-normal text-neutral-900">{{ item.name }}</span>
        <span class="text-[0.75rem] leading-[1.125rem] text-[#747474]">{{ item.priceDetails }}</span>
      </div>
      <div class="flex flex-col items-end" *ngIf="item.hasQuantityInfo; else simplePrice">
        <span class="text-[0.875rem] leading-5 font-bold text-neutral-700">{{ item.totalPrice }}</span>
        <span class="mt-1 px-2 text-[0.6875rem] leading-[1.3rem] rounded-xl bg-[#f1f4fe]">{{ item.quantityInfo }}</span>
      </div>
      <ng-template #simplePrice>
        <span class="text-[0.875rem] leading-5 font-bold text-neutral-700">{{ item.totalPrice }}</span>
      </ng-template>
    </div>
  </div>

