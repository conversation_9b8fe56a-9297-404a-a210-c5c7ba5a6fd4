import { Component, input, output } from '@angular/core';
import { ColorService } from '../../services/color.service';
import { TAILWIND_COLOR_CLASSES } from '../../constants/colors.constant';

@Component({
  selector: 'app-dicount-applied-modal',
  imports: [],
  templateUrl: './dicount-applied-modal.html',
  styleUrl: './dicount-applied-modal.css'
})
export class DicountAppliedModal {
  readonly code = input<string>();
  readonly discount = input<number>(0);
  readonly closeModal = output<void>();

  constructor(private colorService: ColorService) {}

  // Color getters for template use
  get colors() {
    return {
      success: TAILWIND_COLOR_CLASSES['bg-success']
    };
  }

  onClose() {
    this.closeModal.emit();
  }
}
